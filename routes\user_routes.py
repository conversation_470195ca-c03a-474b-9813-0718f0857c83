from flask import Blueprint, render_template, request, redirect, url_for, flash, session, g
from data import location_data
from services import account_service, community_service, event_service, journey_service, subscription_service, user_service
from utils.security import admin_required, admin_or_support_tech_required, login_required
from utils.file_utils import validate_profile_image
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

bp = Blueprint('user', __name__, url_prefix='/user')

@bp.route('/manage', methods=['GET'])
@admin_or_support_tech_required
def get_users():
    """Get list of users for management with tabs for all, staff, blocked, and banned users"""
    search_term = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    active_tab = request.args.get('active_tab', 'all')
    limit = 10
    offset = (page - 1) * limit

    # Set default tab to 'all' if invalid tab provided
    if active_tab not in ['all', 'staff', 'blocked', 'banned']:
        active_tab = 'all'

    # Apply search to different tabs
    if search_term:
        search_term = search_term.strip()

    # Different queries based on active tab with search applied
    if active_tab == 'staff':
        if search_term:
            # Search only within staff accounts
            users_list = user_service.search_users(
                search_term, limit=limit, offset=offset, filter_role=['editor', 'admin']
            )
            total_count = user_service.count_search_results(
                search_term, filter_role=['editor', 'admin']
            )
        else:
            users_list = user_service.get_staff_accounts(limit=limit, offset=offset)
            total_count = user_service.get_staff_count()
    elif active_tab == 'blocked':
        if search_term:
            # Search only within blocked accounts
            users_list = user_service.search_users(
                search_term, limit=limit, offset=offset, filter_blocked=True
            )
            total_count = user_service.count_search_results(
                search_term, filter_blocked=True
            )
        else:
            users_list = user_service.get_blocked_users(limit=limit, offset=offset)
            total_count = user_service.get_blocked_users_count()
    elif active_tab == 'banned':
        if search_term:
            # Search only within banned accounts
            users_list = user_service.search_users(
                search_term, limit=limit, offset=offset, filter_banned=True
            )
            total_count = user_service.count_search_results(
                search_term, filter_banned=True
            )
        else:
            users_list = user_service.get_banned_users(limit=limit, offset=offset)
            total_count = user_service.get_banned_users_count()
    else:  # 'all' tab
        if search_term:
            users_list = user_service.search_users(search_term, limit=limit, offset=offset)
            total_count = user_service.count_search_results(search_term)
        else:
            users_list = user_service.get_users(limit=limit, offset=offset)
            total_count = user_service.get_users_count()

    # Validate profile images - set to None if file doesn't exist
    for user in users_list:
        if user.get('profile_image') and not validate_profile_image(user['profile_image']):
            user['profile_image'] = None

    total_pages = (total_count + limit - 1) // limit

    return render_template(
        'admin/user/list.html',
        users=users_list,
        search_term=search_term,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        active_tab=active_tab,
    )

# Public users route for discovery page
@bp.route('/', methods=['GET'])
@login_required
def get_public_users():
    """Get list of public users for the discovery page"""
    search_term = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    active_tab = request.args.get('active_tab', 'users')
    limit = 8
    offset = (page - 1) * limit


    # Get public users
    if search_term:
        search_term = search_term.strip()
        users_list = user_service.search_users(
            search_term, limit=limit, offset=offset, public_only=True, filter_role=['traveller']
        )
        total_count = user_service.count_search_results(
            search_term, public_only=True, filter_role=['traveller']
        )
    else:
        current_user_id = session.get('user_id')
        users_list = user_service.get_travellers(limit=limit, offset=offset, public_only=True, exclude_user_id=current_user_id)
        total_count = user_service.get_travellers_count(public_only=True, exclude_user_id=current_user_id)

    # Validate profile images - set to None if file doesn't exist
    for user in users_list:
        if user.get('profile_image') and not validate_profile_image(user['profile_image']):
            user['profile_image'] = None

    total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1

    return render_template(
        'discovery/user/list.html',
        users=users_list,
        search_term=search_term,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        active_tab=active_tab
    )

@bp.route('/manage', methods=['GET'])
@admin_or_support_tech_required
def manage_users():
    """Get list of users for management with tabs for all, staff, blocked, and banned users"""
    search_term = request.args.get('q', '')
    page = request.args.get('page', 1, type=int)
    active_tab = request.args.get('active_tab', 'all')
    limit = 10
    offset = (page - 1) * limit

    # Set default tab to 'all' if invalid tab provided
    if active_tab not in ['all', 'staff', 'blocked', 'banned']:
        active_tab = 'all'

    # Apply search to different tabs
    if search_term:
        search_term = search_term.strip()

    # Different queries based on active tab with search applied
    if active_tab == 'staff':
        if search_term:
            # Search only within staff accounts
            users_list = user_service.search_users(
                search_term, limit=limit, offset=offset, filter_role=['editor', 'admin']
            )
            total_count = user_service.count_search_results(
                search_term, filter_role=['editor', 'admin']
            )
        else:
            users_list = user_service.get_staff_accounts(limit=limit, offset=offset)
            total_count = user_service.get_staff_count()
    elif active_tab == 'blocked':
        if search_term:
            # Search only within blocked accounts
            users_list = user_service.search_users(
                search_term, limit=limit, offset=offset, filter_blocked=True
            )
            total_count = user_service.count_search_results(
                search_term, filter_blocked=True
            )
        else:
            users_list = user_service.get_blocked_users(limit=limit, offset=offset)
            total_count = user_service.get_blocked_users_count()
    elif active_tab == 'banned':
        if search_term:
            # Search only within banned accounts
            users_list = user_service.search_users(
                search_term, limit=limit, offset=offset, filter_banned=True
            )
            total_count = user_service.count_search_results(
                search_term, filter_banned=True
            )
        else:
            users_list = user_service.get_banned_users(limit=limit, offset=offset)
            total_count = user_service.get_banned_users_count()
    else:  # 'all' tab
        if search_term:
            users_list = user_service.search_users(search_term, limit=limit, offset=offset)
            total_count = user_service.count_search_results(search_term)
        else:
            users_list = user_service.get_users(limit=limit, offset=offset)
            total_count = user_service.get_users_count()

    # Validate profile images - set to None if file doesn't exist
    for user in users_list:
        if user.get('profile_image') and not validate_profile_image(user['profile_image']):
            user['profile_image'] = None

    total_pages = (total_count + limit - 1) // limit

    return render_template(
        'admin/user/list.html',
        users=users_list,
        search_term=search_term,
        page=page,
        total_pages=total_pages,
        total_count=total_count,
        active_tab=active_tab
    )


@bp.route('/manage/<int:user_id>/', methods=['GET'])
@admin_or_support_tech_required
def get_user(user_id):
    """Get user details with tabbed structure similar to account page"""
    user = user_service.get_user_by_id(user_id)
    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('user.manage_users'))

    # Get active tab, default to 'profile' if not specified
    active_tab = request.args.get('active_tab', 'profile')
    search_term = request.args.get('q', '')
    referrer = request.args.get('referrer')

    # Verify profile image exists
    if user.get('profile_image') and not validate_profile_image(user['profile_image']):
        user['profile_image'] = None

    # Prepare context with base user information
    context = {
        'user': user,
        'current_user': g.current_user,
        'active_tab': active_tab,
        'search_term': search_term,
        'referrer': referrer,
        'user_role': user.get('role')
    }

    # Add tab-specific data
    if active_tab == 'profile':
        # Additional profile data could be added here if needed
        pass

    elif active_tab == 'activities':
        account = user.copy()
        account['public_journeys'] = journey_service.get_public_journeys(user_id=user_id, limit=10, offset=0)
        account['visited_places'] = location_data.get_user_visited_locations(user_id, limit=10)
        account['recent_likes'] = community_service.get_user_liked_events(user_id, limit=5)
        account['recent_comments'] = community_service.get_user_comments(user_id, limit=5)
        _, _, account['events'] = event_service.get_user_private_events(user_id=account['id'])
        account['show_public_journeys'] = True
        account['show_visited_places'] = True
        account['show_recent_likes'] = True
        account['show_recent_comments'] = True
        context['account'] = account
        context['activities_tab'] = request.args.get('activities_tab', 'journeys')

    elif active_tab == 'management':
        # This is primarily for management actions
        # Those functions are likely handled by the template and AJAX calls
        pass

    # Add subscription information if needed - for all tabs or specific tabs
    try:
        # Get subscription history
        page = request.args.get('page', 1, type=int)
        per_page = 5
        subscriptions, total = subscription_service.get_user_subscription_history(user_id, per_page, (page - 1) * per_page)
        total_pages = (total + per_page - 1) // per_page

        # Get active subscription
        active_subscription = subscription_service.get_user_active_subscription(user_id)

        # Get payment history
        payments = subscription_service.get_user_payments(user_id)
        latest_payment = payments[0] if payments else None

        # Patch period_months if missing/zero using subscription's own months field
        for sub in subscriptions:
            period = sub.get('period_months')
            if not period or period == 0:
                if sub.get('months'):
                    sub['period_months'] = sub['months']

        # Add to context
        context.update({
            'active_subscription': active_subscription,
            'all_subscriptions': subscriptions,
            'payments': payments,
            'latest_payment': latest_payment,
            'page': page,
            'total_pages': total_pages,
            'active_tab': active_tab
        })
    except Exception as e:
        logger.error(f"Error fetching subscription data: {str(e)}")
        flash("Could not load subscription information", "warning")

    return render_template('admin/user/detail.html', **context)


@bp.route('/manage/<int:user_id>/role', methods=['POST'])
@admin_required
def update_user_role(user_id):
    """Update user role"""
    # Get user for validation
    user = account_service.get_user_profile(user_id)

    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('user.manage_users'))

    # Preserve referrer parameter if it exists
    referrer = request.args.get('referrer')

    # Prevent self-role modification
    if int(session['user_id']) == int(user_id):
        flash('You cannot modify your own role', 'danger')
        return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))

    from utils.permissions import Roles
    role = request.form.get('role')
    valid_roles = [Roles.TRAVELLER, Roles.EDITOR, Roles.MODERATOR, Roles.SUPPORT_TECH, Roles.ADMIN]
    if role not in valid_roles:
        flash('Invalid role specified', 'danger')
        return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))

    success, message = user_service.update_user_role(
        admin_id=session['user_id'],
        user_id=user_id,
        role=role
    )

    flash(message, 'success' if success else 'danger')

    # If referrer is hidden_journeys, redirect back to that page
    if referrer == 'hidden_journeys':
        return redirect(url_for('journey.hidden_journeys'))

    return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))


@bp.route('/manage/<int:user_id>/status/block', methods=['POST'])
@admin_or_support_tech_required
def update_user_block_status(user_id):
    """Update user block status"""
    # Get user for validation
    user = account_service.get_user_profile(user_id)

    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('user.manage_users'))

    # Preserve referrer parameter if it exists
    referrer = request.args.get('referrer')

    # Prevent self-blocking
    if int(session['user_id']) == int(user_id):
        flash('You cannot modify your own sharing status', 'danger')
        return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))

    is_blocked = not user['is_blocked']
    success, message = user_service.update_user_block_status(
        admin_id=session['user_id'],
        user_id=user_id,
        is_blocked=is_blocked
    )

    flash(message, 'success' if success else 'danger')

    # If referrer is hidden_journeys, redirect back to that page
    if referrer == 'hidden_journeys':
        return redirect(url_for('journey.hidden_journeys'))

    return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))


@bp.route('/manage/<int:user_id>/status/ban', methods=['POST'])
@admin_or_support_tech_required
def update_user_ban_status(user_id):
    """Update user ban status"""
    # Get user for validation
    user = account_service.get_user_profile(user_id)

    if not user:
        flash('User not found', 'danger')
        return redirect(url_for('user.manage_users'))

    # Preserve referrer parameter if it exists
    referrer = request.args.get('referrer')

    # Prevent self-banning
    if int(session['user_id']) == int(user_id):
        flash('You cannot modify your own account status and role', 'danger')
        return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))

    is_banned = not user['is_banned']
    success, message = user_service.update_user_ban_status(
        admin_id=session['user_id'],
        user_id=user_id,
        is_banned=is_banned
    )

    flash(message, 'success' if success else 'danger')

    # If referrer is hidden_journeys, redirect back to that page
    if referrer == 'hidden_journeys':
        return redirect(url_for('journey.hidden_journeys'))

    return redirect(url_for('user.get_user', user_id=user_id, referrer=referrer))


@bp.route('/manage/<int:user_id>/gift-subscription', methods=['POST'])
@admin_or_support_tech_required
def gift_subscription(user_id):
    try:
        months = int(request.form.get('months', 1) or 1)
    except (ValueError, TypeError):
        months = 1
    if months < 1:
        months = 1
    reason = request.form.get('reason', '')
    admin_id = session.get('user_id')
    success, message, _ = subscription_service.create_gift_subscription(admin_id, user_id, months, reason)
    flash(message, 'success' if success else 'danger')
    return redirect(url_for('user.get_user', user_id=user_id, active_tab='subscription'))


